"""
ShillRank - 币圈 KOL 影响力排行榜
主应用程序
"""
import gradio as gr
import pandas as pd
import plotly.graph_objects as go
from modules.analyzer import ShillRankAnalyzer
from utils.helpers import (
    ensure_cache_dir, create_ranking_chart, create_price_impact_chart,
    create_interaction_chart, create_mentions_frequency_chart,
    format_kol_table, export_results
)
from config import APP_TITLE, APP_DESCRIPTION, DEFAULT_DAYS, MAX_DAYS
import os

# 确保必要目录存在
ensure_cache_dir()

# 初始化分析器
analyzer = ShillRankAnalyzer()

def analyze_coin_influence(coin_query: str, days: int, progress=gr.Progress()):
    """分析币种影响力的主函数"""
    if not coin_query.strip():
        return (
            "❌ 请输入币种名称",
            pd.DataFrame(),
            go.Figure(),
            go.Figure(),
            go.Figure(),
            go.Figure(),
            ""
        )
    
    progress(0.1, desc="开始分析...")
    
    try:
        # 执行分析
        progress(0.3, desc="搜索币种信息...")
        result = analyzer.analyze_coin_influence(coin_query.strip(), days)
        
        if 'error' in result:
            return (
                f"❌ {result['error']}",
                pd.DataFrame(),
                go.Figure(),
                go.Figure(),
                go.Figure(),
                go.Figure(),
                ""
            )
        
        progress(0.7, desc="生成图表...")
        
        # 生成图表
        ranking_chart = create_ranking_chart(result['kol_rankings'])
        price_chart = create_price_impact_chart(result['kol_rankings'])
        interaction_chart = create_interaction_chart(result['kol_rankings'])
        frequency_chart = create_mentions_frequency_chart(result['kol_rankings'])
        
        # 格式化表格
        table_df = format_kol_table(result['kol_rankings'])
        
        progress(0.9, desc="生成总结...")
        
        # 构建状态信息
        coin_info = result['coin_info']
        current_price = result.get('current_price', {})
        
        status_info = f"""
✅ 分析完成！

🪙 **币种信息**
• 名称: {coin_info['name']} ({coin_info['symbol'].upper()})
• CoinGecko ID: {coin_info['id']}
"""
        
        if current_price:
            price_usd = current_price.get('usd', 0)
            change_24h = current_price.get('usd_24h_change', 0)
            market_cap = current_price.get('usd_market_cap', 0)
            
            status_info += f"""
• 当前价格: ${price_usd:.6f}
• 24h 变化: {change_24h:.2f}%
• 市值: ${market_cap:,.0f}
"""
        
        status_info += f"""
📊 **分析结果**
• 分析天数: {days} 天
• 活跃 KOL: {len(result['kol_rankings'])} 位
• 总提及次数: {sum(kol['coin_mentions'] for kol in result['kol_rankings'])}

{result['summary']}
"""
        
        progress(1.0, desc="完成!")
        
        return (
            status_info,
            table_df,
            ranking_chart,
            price_chart,
            interaction_chart,
            frequency_chart,
            result['summary']
        )
        
    except Exception as e:
        error_msg = f"❌ 分析过程中出现错误: {str(e)}"
        return (
            error_msg,
            pd.DataFrame(),
            go.Figure(),
            go.Figure(),
            go.Figure(),
            go.Figure(),
            ""
        )

def create_interface():
    """创建 Gradio 界面"""
    
    with gr.Blocks(
        title="ShillRank",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .main-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        """
    ) as app:
        
        # 标题和描述
        gr.HTML(f"""
        <div class="main-header">
            <h1>{APP_TITLE}</h1>
            <p>{APP_DESCRIPTION}</p>
        </div>
        """)
        
        # 输入区域
        with gr.Row():
            with gr.Column(scale=2):
                coin_input = gr.Textbox(
                    label="🪙 币种名称",
                    placeholder="输入币种名称，如: PEPE, DOGE, BTC...",
                    value="PEPE"
                )
            
            with gr.Column(scale=1):
                days_input = gr.Slider(
                    minimum=1,
                    maximum=MAX_DAYS,
                    value=DEFAULT_DAYS,
                    step=1,
                    label="📅 分析天数"
                )
            
            with gr.Column(scale=1):
                analyze_btn = gr.Button(
                    "🚀 开始分析",
                    variant="primary",
                    size="lg"
                )
        
        # 状态显示
        status_output = gr.Markdown(label="📊 分析状态")
        
        # 结果展示区域
        with gr.Tabs():
            # 排行榜标签页
            with gr.TabItem("🏆 KOL 排行榜"):
                with gr.Row():
                    table_output = gr.Dataframe(
                        label="KOL 影响力排行榜",
                        interactive=False,
                        wrap=True
                    )
                
                with gr.Row():
                    ranking_chart_output = gr.Plot(label="综合评分对比")
            
            # 详细分析标签页
            with gr.TabItem("📈 详细分析"):
                with gr.Row():
                    with gr.Column():
                        price_chart_output = gr.Plot(label="价格影响力")
                    with gr.Column():
                        interaction_chart_output = gr.Plot(label="互动影响力")
                
                with gr.Row():
                    frequency_chart_output = gr.Plot(label="提及频次")
            
            # AI 总结标签页
            with gr.TabItem("🤖 AI 总结"):
                summary_output = gr.Markdown(label="智能分析总结")
        
        # 示例
        gr.Examples(
            examples=[
                ["PEPE", 7],
                ["DOGE", 14],
                ["SHIB", 7],
                ["BTC", 30],
                ["ETH", 14]
            ],
            inputs=[coin_input, days_input],
            label="💡 示例"
        )
        
        # 绑定事件
        analyze_btn.click(
            fn=analyze_coin_influence,
            inputs=[coin_input, days_input],
            outputs=[
                status_output,
                table_output,
                ranking_chart_output,
                price_chart_output,
                interaction_chart_output,
                frequency_chart_output,
                summary_output
            ]
        )
        
        # 页脚信息
        gr.HTML("""
        <div style="text-align: center; margin-top: 2rem; padding: 1rem; border-top: 1px solid #eee;">
            <p>🔧 技术栈: Gradio + snscrape + CoinGecko API + Hugging Face</p>
            <p>📊 数据来源: Twitter + CoinGecko | 🤖 AI 分析: Hugging Face Transformers</p>
        </div>
        """)
    
    return app

if __name__ == "__main__":
    # 创建应用
    app = create_interface()
    
    # 启动应用
    app.launch(
        share=True,
        debug=False
    )
