# 💥 ShillRank - 币圈 KOL 影响力排行榜

🧠 **一句话简介**: 基于推文和行情数据，量化分析币圈 KOL 喊单影响力的排行榜系统。

## 🚀 快速开始

### 1. 安装依赖

```bash
cd ShillRank
pip install -r requirements.txt
```

### 2. 配置环境（可选）

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑 .env 文件，添加 Hugging Face API Token（可选）
# 获取地址: https://huggingface.co/settings/tokens
```

### 3. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:7860` 启动。

## 🧩 功能特性

### ✅ 核心功能
- 🔍 **币种搜索**: 支持币种名称、符号搜索
- 📊 **KOL 分析**: 分析 10+ 知名 KOL 的推文影响力
- 💹 **价格影响**: 计算推文发布后的价格变化
- 📈 **互动分析**: 统计点赞、转发、评论等互动数据
- 🤖 **AI 总结**: 使用 Hugging Face 模型生成智能分析

### 📊 评分维度
- **价格影响** (40%): 推文后币价涨跌幅
- **互动影响** (30%): 平均点赞、转发数
- **提及频次** (20%): 提及目标币种的次数
- **情绪分析** (10%): 推文情绪倾向

### 📈 可视化图表
- 🏆 KOL 综合排行榜
- 💹 价格影响力对比
- 👥 互动数据分析
- 📊 提及频次统计

## 🛠️ 技术架构

```
┌─────────── UI 层 ──────────┐
│        Gradio Web UI       │
└────────────────────────────┘
            ↓
┌─────────── 应用层 ─────────┐
│  • KOL 数据抓取 (snscrape)  │
│  • 币价追踪 (CoinGecko)    │
│  • AI 分析 (Hugging Face)  │
│  • 评分计算 & 排名         │
└────────────────────────────┘
            ↓
┌─────────── 数据层 ─────────┐
│  • Twitter 推文数据        │
│  • CoinGecko 价格数据      │
│  • 本地缓存 (diskcache)    │
└────────────────────────────┘
```

## 📁 项目结构

```
ShillRank/
├── app.py                 # Gradio 主应用
├── config.py             # 配置文件
├── requirements.txt      # 依赖包
├── modules/              # 核心模块
│   ├── analyzer.py       # 主分析器
│   ├── kol_scraper.py    # 推文抓取
│   ├── price_tracker.py  # 价格追踪
│   └── llm_analyzer.py   # AI 分析
├── utils/                # 工具函数
│   └── helpers.py        # 图表生成等
└── data/                 # 数据文件
    ├── kol_list.json     # KOL 列表
    └── cache/            # 缓存目录
```

## 🎯 使用方法

1. **输入币种**: 在输入框中输入币种名称（如 PEPE、DOGE、BTC）
2. **选择时间**: 选择分析的天数范围（1-30天）
3. **开始分析**: 点击"开始分析"按钮
4. **查看结果**: 
   - 🏆 查看 KOL 排行榜
   - 📈 分析详细图表
   - 🤖 阅读 AI 总结

## 🔧 自定义配置

### 添加新的 KOL

编辑 `data/kol_list.json` 文件：

```json
{
  "kols": [
    {
      "username": "new_kol_username",
      "display_name": "KOL 显示名称",
      "category": "分类标签"
    }
  ]
}
```

### 调整评分权重

编辑 `config.py` 中的 `SCORE_WEIGHTS`：

```python
SCORE_WEIGHTS = {
    "price_impact": 0.4,    # 价格影响权重
    "interactions": 0.3,    # 互动数权重
    "frequency": 0.2,       # 提及频次权重
    "sentiment": 0.1        # 情绪权重
}
```

## 🚀 部署到 Modal

```python
# modal_app.py
import modal

app = modal.App("shillrank")

@app.function(
    image=modal.Image.debian_slim().pip_install_from_requirements("requirements.txt"),
    ports=[7860]
)
def run_app():
    import subprocess
    subprocess.run(["python", "app.py"])

if __name__ == "__main__":
    app.run()
```

## ⚠️ 注意事项

1. **数据获取限制**: snscrape 可能受到 Twitter 反爬限制
2. **API 限制**: CoinGecko 免费版有请求频率限制
3. **数据准确性**: 价格影响分析仅供参考，不构成投资建议
4. **缓存机制**: 系统使用缓存减少重复请求

## 🔮 未来功能

- [ ] 实时监控和预警
- [ ] 更多 KOL 数据源
- [ ] 高级情绪分析
- [ ] 历史趋势分析
- [ ] API 接口开放

## 📄 许可证

MIT License

---

**免责声明**: 本工具仅用于数据分析和研究目的，不构成任何投资建议。
