"""
系统测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.price_tracker import PriceTracker
from modules.kol_scraper import KOLScraper
from modules.analyzer import ShillRankAnalyzer
from utils.helpers import ensure_cache_dir

def test_price_tracker():
    """测试价格追踪模块"""
    print("🧪 测试价格追踪模块...")
    
    tracker = PriceTracker()
    
    # 测试搜索币种
    coin = tracker.search_coin("PEPE")
    if coin:
        print(f"✅ 找到币种: {coin['name']} ({coin['symbol']})")
        
        # 测试获取当前价格
        current_price = tracker.get_current_price(coin['id'])
        if current_price:
            print(f"✅ 当前价格: ${current_price.get('usd', 'N/A')}")
        else:
            print("❌ 获取当前价格失败")
    else:
        print("❌ 搜索币种失败")

def test_kol_scraper():
    """测试 KOL 抓取模块"""
    print("\n🧪 测试 KOL 抓取模块...")
    
    scraper = KOLScraper()
    
    # 测试抓取推文（使用较小的数据量）
    tweets = scraper.scrape_user_tweets("elonmusk", days=1)
    print(f"✅ 抓取到 {len(tweets)} 条推文")
    
    if tweets:
        # 测试筛选币种提及
        coin_tweets = scraper.filter_coin_mentions(tweets, "DOGE", "Dogecoin")
        print(f"✅ 找到 {len(coin_tweets)} 条提及 DOGE 的推文")

def test_full_analysis():
    """测试完整分析流程"""
    print("\n🧪 测试完整分析流程...")
    
    analyzer = ShillRankAnalyzer()
    
    # 测试分析（使用较短时间范围）
    result = analyzer.analyze_coin_influence("DOGE", days=1)
    
    if 'error' in result:
        print(f"❌ 分析失败: {result['error']}")
    else:
        print(f"✅ 分析成功!")
        print(f"   币种: {result['coin_info']['name']}")
        print(f"   活跃 KOL: {len(result['kol_rankings'])}")
        if result['kol_rankings']:
            top_kol = result['kol_rankings'][0]
            print(f"   排名第一: {top_kol['display_name']} (评分: {top_kol['scores']['total']:.2f})")

def main():
    """主测试函数"""
    print("🚀 ShillRank 系统测试")
    print("=" * 50)
    
    # 确保目录存在
    ensure_cache_dir()
    
    try:
        # 测试各个模块
        test_price_tracker()
        test_kol_scraper()
        test_full_analysis()
        
        print("\n" + "=" * 50)
        print("✅ 系统测试完成！")
        print("💡 现在可以运行 'python app.py' 启动 Web 界面")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        print("💡 请检查网络连接和依赖安装")

if __name__ == "__main__":
    main()
