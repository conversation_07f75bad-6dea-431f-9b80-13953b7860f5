"""
ShillRank 功能测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.analyzer import ShillRankAnalyzer
from modules.price_tracker import PriceTracker
from modules.kol_scraper import KOLScraper
from utils.helpers import ensure_cache_dir, format_kol_table

def test_with_demo_data():
    """使用演示数据测试完整流程"""
    print("🚀 ShillRank 功能演示")
    print("=" * 50)
    
    # 确保目录存在
    ensure_cache_dir()
    
    # 初始化分析器
    analyzer = ShillRankAnalyzer()
    
    print("🔍 开始分析 DOGE 的 KOL 影响力...")
    
    # 执行分析
    result = analyzer.analyze_coin_influence("DOGE", days=7)
    
    if 'error' in result:
        print(f"❌ 分析失败: {result['error']}")
        return
    
    print("✅ 分析成功!")
    print(f"   币种: {result['coin_info']['name']} ({result['coin_info']['symbol']})")
    print(f"   活跃 KOL: {len(result['kol_rankings'])}")
    
    if result['kol_rankings']:
        print("\n🏆 KOL 排行榜:")
        print("-" * 80)
        
        for kol in result['kol_rankings'][:5]:  # 显示前5名
            print(f"#{kol['rank']} {kol['display_name']} (@{kol['username']})")
            print(f"   提及次数: {kol['coin_mentions']}")
            print(f"   平均互动: {kol['avg_interactions']:.0f}")
            print(f"   价格影响: {kol.get('avg_price_impact', 0):.2f}%")
            print(f"   综合评分: {kol['scores']['total']:.2f}")
            print()
        
        print("📊 生成表格数据...")
        table_df = format_kol_table(result['kol_rankings'])
        print(table_df.to_string(index=False))
        
        print(f"\n🤖 AI 总结:")
        print(result['summary'])
    else:
        print("   没有找到相关的 KOL 提及数据")
    
    print("\n" + "=" * 50)
    print("✅ 演示完成!")
    
    return result

def test_individual_modules():
    """测试各个模块"""
    print("\n🧪 模块功能测试")
    print("-" * 30)
    
    # 测试价格追踪
    print("1. 测试价格追踪模块...")
    tracker = PriceTracker()
    coin = tracker.search_coin("DOGE")
    if coin:
        print(f"   ✅ 找到币种: {coin['name']}")
        current_price = tracker.get_current_price(coin['id'])
        if current_price:
            print(f"   ✅ 当前价格: ${current_price.get('usd', 'N/A')}")
    
    # 测试 KOL 抓取（演示模式）
    print("\n2. 测试 KOL 抓取模块...")
    scraper = KOLScraper()
    tweets = scraper.scrape_user_tweets("elonmusk", days=1)
    print(f"   ✅ 获取推文: {len(tweets)} 条")
    
    if tweets:
        coin_tweets = scraper.filter_coin_mentions(tweets, "DOGE", "Dogecoin")
        print(f"   ✅ DOGE 相关推文: {len(coin_tweets)} 条")

def main():
    """主函数"""
    try:
        # 测试各个模块
        test_individual_modules()
        
        # 测试完整流程
        result = test_with_demo_data()
        
        print("\n💡 使用说明:")
        print("1. 当前系统使用演示数据（由于网络限制）")
        print("2. 在正常网络环境下，系统会自动抓取真实的 Twitter 数据")
        print("3. 价格数据来自 CoinGecko API，是真实的")
        print("4. 可以通过修改 data/kol_list.json 添加更多 KOL")
        
        print("\n🚀 启动 Web 界面:")
        print("运行 'python3 app.py' 启动完整的 Gradio 界面")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
